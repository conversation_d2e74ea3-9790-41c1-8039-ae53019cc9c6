import React, { useState } from "react";
import {
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  ModalBody,
  ModalFooter,
  Select,
  useToast,
  NumberInput,
  NumberInputField,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";

const EditBlockModal = ({ viewBlock, onClose, blockId, renderMeFn }) => {
  const [updateBtnLoading, setUpdateBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];

  // Validation Schema
  const validationSchema = Yup.object().shape({
    identity: Yup.string().required("Identity is required"),
    title: Yup.string()
      .required("Title is required")
      .min(3, "Title should be minimum 3 characters"),
    visibility: Yup.boolean().required("Visibility is required"),
    position: Yup.number()
      .required("Position is required")
      .positive("Please enter a positive value"),
    max: Yup.number().nullable().positive("Please enter a positive value"),
    collectionName: Yup.string().required("Collection name is required"),
  });

  //  Formik
  const formik = useFormik({
    initialValues: {
      identity: viewBlock?.identity || "",
      title: viewBlock?.title || "",
      visibility: viewBlock?.visibility ?? true,
      position: viewBlock?.position || "",
      max: viewBlock?.max || "",
      collectionName: viewBlock?.collectionName || "",
    },
    validationSchema,
    onSubmit: async (values) => {
      setUpdateBtnLoading(true);
      try {
        await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy-cms/update/block/${blockId}`,
          values,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setUpdateBtnLoading(false);
        onClose();
        renderMeFn();
        toast({
          title: "Block updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      } catch (error) {
        setUpdateBtnLoading(false);
        console.log(error);
        toast({
          title:
            error.response?.status === 403
              ? "You don't have access to perform this action"
              : "Something went wrong, please try again later",
          status: error.response?.status === 403 ? "warning" : "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    },
  });

  return (
    <>
      <ModalBody px={{ base: 4, md: 6 }}>
        {/* Identity */}
        <FormControl isInvalid={formik.touched.identity && formik.errors.identity} mb={4}>
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Identity</FormLabel>
          <Input
            id="identity"
            name="identity"
            placeholder="Enter block identity"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.identity}
            size="md"
          />
          <FormErrorMessage>{formik.errors.identity}</FormErrorMessage>
        </FormControl>

        {/* Title */}
        <FormControl isInvalid={formik.touched.title && formik.errors.title} mb={4}>
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Title</FormLabel>
          <Input
            id="title"
            name="title"
            placeholder="Enter block title"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.title}
            size="md"
          />
          <FormErrorMessage>{formik.errors.title}</FormErrorMessage>
        </FormControl>

        {/* Position */}
        <FormControl isInvalid={formik.touched.position && formik.errors.position} mb={4}>
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Position</FormLabel>
          <NumberInput
            value={formik.values.position}
            onChange={(value) =>
              formik.setFieldValue("position", parseInt(value) || "")
            }
            size="md"
            min={1}
          >
            <NumberInputField placeholder="Enter position" onBlur={formik.handleBlur} />
          </NumberInput>
          <FormErrorMessage>{formik.errors.position}</FormErrorMessage>
        </FormControl>

        {/* Max */}
        <FormControl isInvalid={formik.touched.max && formik.errors.max} mb={4}>
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Max</FormLabel>
          <Select
            id="max"
            name="max"
            value={formik.values.max}
            onChange={e => formik.setFieldValue("max", parseInt(e.target.value) || "")}
            size="md"
            width="100px"
            minW="100px"
            maxW="100px"
          >
            {[...Array(15)].map((_, i) => (
              <option key={i + 1} value={i + 1}>{i + 1}</option>
            ))}
          </Select>
          <FormErrorMessage>{formik.errors.max}</FormErrorMessage>
        </FormControl>

        {/* Collection Name */}
        <FormControl
          isInvalid={formik.touched.collectionName && formik.errors.collectionName}
          mb={4}
        >
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Collection Name</FormLabel>
          <Select
            id="collectionName"
            name="collectionName"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.collectionName}
            size="md"
          >
            <option value="">Select collection type</option>
            <option value="academyFacilities">Top Facility</option>
            <option value="academyTopCoachCms">Top Coaches</option>
            <option value="academyTopCourseCms">Top Courses</option>
            <option value="academyTestimonial">Testimonials</option>
            <option value="academyDescription">Description</option>
          </Select>
          <FormErrorMessage>{formik.errors.collectionName}</FormErrorMessage>
        </FormControl>

        {/* Visibility */}
        <FormControl
          isInvalid={formik.touched.visibility && formik.errors.visibility}
          mb={4}
        >
          <FormLabel fontSize={{ base: "sm", md: "md" }}>Visibility</FormLabel>
          <Select
            id="visibility"
            name="visibility"
            onChange={(e) => formik.setFieldValue("visibility", e.target.value === "true")}
            value={formik.values.visibility}
            size="md"
          >
            <option value={true}>Visible</option>
            <option value={false}>Not Visible</option>
          </Select>
          <FormErrorMessage>{formik.errors.visibility}</FormErrorMessage>
        </FormControl>
      </ModalBody>

      <ModalFooter
        px={{ base: 4, md: 6 }}
        flexDirection={{ base: "column", sm: "row" }}
        gap={{ base: 3, sm: 0 }}
      >
        <Button
          colorScheme="red"
          variant="outline"
          size="sm"
          w={{ base: "full", sm: "auto" }}
          onClick={onClose}
        >
          Discard
        </Button>
        <Button
          variant="solid"
          colorScheme="green"
          size="sm"
          ml={{ sm: 3 }}
          mt={{ base: 3, sm: 0 }}
          w={{ base: "full", sm: "auto" }}
          onClick={formik.handleSubmit}
          isLoading={updateBtnLoading}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};

export default EditBlockModal;
